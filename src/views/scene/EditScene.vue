<template>
    <div class="edit-scene">
        <el-breadcrumb separator="/" class="edit-breadcrumb" style="margin-bottom: 18px;">
            <el-breadcrumb-item :to="{ path: '/scene/manage' }">场景管理</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/scene/edit/'+ form.scene_id }">{{ form.scene_name }}</el-breadcrumb-item>
            <el-breadcrumb-item>场景编辑</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="edit-actions">
            <el-button type="primary" size="small" @click="openAIOptimize" :disabled="!form.scene_id">AI优化提示词</el-button>
            <el-button type="danger" size="small" @click="handleDeleteScene" :disabled="!form.scene_id">删除场景</el-button>
        </div>
        <el-steps :active="activeStep" finish-status="success" simple>
            <el-step title="基本信息" />
            <el-step title="计算基准线配置" />
            <el-step title="数据输入平台" />
            <el-step title="数据输出平台" />
            <el-step title="其他设置" />
        </el-steps>

        <el-form ref="form" :model="form" :rules="rules" label-width="150px" class="mt-20" v-loading="loading">
            <div v-show="activeStep === 0">
                <el-form-item label="场景名称" prop="scene_name">
                    <el-input v-model="form.scene_name" placeholder="请输入场景名称"></el-input>
                </el-form-item>
                <el-form-item label="任务类型" prop="linked_task_type_code">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <el-select v-model="form.linked_task_type_code" placeholder="请选择任务类型" style="flex: 1;" @change="handleTaskTypeChange">
                            <el-option
                                v-for="taskType in taskTypes"
                                :key="taskType.task_type_code"
                                :label="taskType.task_type_name"
                                :value="taskType.task_type_code">
                            </el-option>
                        </el-select>
                        <el-button type="primary" size="small" icon="el-icon-plus" @click="showAddTaskTypeDialog">
                            新增任务类型
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item label="场景类型" prop="scene_business_type">
                    <el-select
                        v-model="form.scene_business_type"
                        placeholder="请选择或输入场景类型"
                        filterable
                        allow-create
                        default-first-option
                        style="width: 100%">
                        <el-option
                            v-for="sceneType in sceneTypes"
                            :key="sceneType.scene_type_id || sceneType.scene_type_code"
                            :label="sceneType.scene_type_name"
                            :value="sceneType.scene_type_name">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目组" prop="linked_project_team_name">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <el-select v-model="form.linked_project_team_name" placeholder="请选择项目组" style="flex: 1;" @change="handleProjectTeamChange">
                            <el-option
                                v-for="project in projectTeams"
                                :key="project.project_id"
                                :label="project.project_name"
                                :value="project.project_name">
                            </el-option>
                        </el-select>
                        <el-button type="primary" size="small" icon="el-icon-plus" @click="showAddProjectDialog">
                            新增项目组
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item label="场景描述" prop="scene_description">
                    <el-input type="textarea" v-model="form.scene_description" placeholder="请输入场景描述"
                        :rows="4"></el-input>
                </el-form-item>
            </div>

            <div v-show="activeStep === 1">
                <el-form-item label="使用数据的起始天数" prop="baseline_data_start_days_ago">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-input-number
                            v-model="form.baseline_data_start_days_ago"
                            :min="1"
                            :max="365"
                            :step="1"
                            placeholder="请输入天数"
                            style="width: 200px">
                        </el-input-number>
                        <span style="color: #909399;">天</span>
                        <el-tooltip effect="dark" placement="top" content="T-基线使用数据的起始天数">
                            <i class="el-icon-question" style="color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="排除最近的数据天数" prop="baseline_data_exclude_recent_days">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-input-number
                            v-model="form.baseline_data_exclude_recent_days"
                            :min="0"
                            :max="365"
                            :step="1"
                            placeholder="请输入天数"
                            style="width: 200px">
                        </el-input-number>
                        <span style="color: #909399;">天</span>
                        <el-tooltip effect="dark" placement="top" content="T-基线排除最近的数据天数">
                            <i class="el-icon-question" style="color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </div>
                </el-form-item>
                <!-- <el-form-item label="样本总数">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-input
                            value="将根据上述配置自动计算"
                            disabled
                            style="width: 300px;">
                        </el-input>
                        <span style="color: #909399; font-size: 12px;">基于起始天数和排除天数范围内的数据量</span>
                    </div>
                </el-form-item> -->
                <el-form-item label="样本量最小阈值" prop="min_baseline_sample_count">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-input-number
                            v-model="form.min_baseline_sample_count"
                            :min="1"
                            :max="10000"
                            :step="1"
                            placeholder="请输入样本量"
                            style="width: 200px">
                        </el-input-number>
                        <span style="color: #909399;">个</span>
                        <el-tooltip effect="dark" placement="top" content="一个场景在一次计算基准线时，所需要的最小样本量">
                            <i class="el-icon-question" style="color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="基准线更新频率" prop="baseline_refresh_frequency_days">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-input-number
                            v-model="form.baseline_refresh_frequency_days"
                            :min="1"
                            :max="365"
                            :step="1"
                            placeholder="请输入频率"
                            style="width: 200px">
                        </el-input-number>
                        <span style="color: #909399;">天</span>
                        <el-tooltip effect="dark" placement="top" content="评估效果的基准线更新的频率">
                            <i class="el-icon-question" style="color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </div>
                </el-form-item>
            </div>

            <div v-show="activeStep === 2">
                <el-form-item label="选择输入平台" prop="input_platforms">
                    <div v-if="!form.linked_task_type_code" class="platform-selection-tip">
                        <el-alert
                            title="请先在第一步选择任务类型"
                            type="info"
                            :closable="false"
                            show-icon>
                        </el-alert>
                    </div>
                    <div v-else class="platform-selection-container">
                        <el-checkbox-group v-model="form.input_platforms" @change="handleInputPlatformChange"
                            class="platform-checkbox-group">
                            <el-checkbox v-for="platform in availablePlatforms" :key="platform.platform_id"
                                :label="platform.platform_id" class="platform-checkbox-item">
                                {{ platform.platform_name }}
                            </el-checkbox>
                        </el-checkbox-group>
                        <div v-if="loadingInputPlatforms" class="loading-tip">
                            <el-alert
                                title="正在加载平台配置，请稍候..."
                                type="info"
                                :closable="false"
                                show-icon>
                            </el-alert>
                        </div>
                        <div v-if="availablePlatforms.length === 0" class="no-platforms-tip">
                            <el-alert
                                title="当前任务类型没有关联的平台"
                                type="warning"
                                :closable="false"
                                show-icon>
                            </el-alert>
                        </div>
                    </div>
                </el-form-item>

                <div v-if="selectedInputPlatforms && selectedInputPlatforms.length > 0" class="platform-configs" v-loading="loadingInputPlatforms" element-loading-text="正在加载平台配置...">
                    <h3>输入平台配置</h3>
                    <el-card v-for="platform in selectedInputPlatforms" :key="platform.platform_id"
                        class="platform-card">
                        <div slot="header">
                            <span>{{ platform.platform_name }}</span>
                        </div>

                        <el-form-item v-for="field in platform.fields" :key="field.field_name" :label="field.label"
                            :prop="'input_platforms_data.' + platform.platform_id + '.' + field.field_name">
                            <component :is="getFieldComponent(field.field_type)"
                                v-model="form.input_platforms_data[platform.platform_id][field.field_name]"
                                v-bind="getFieldProps(field)"></component>
                        </el-form-item>

                        <el-form-item label="数据类型" :prop="'input_data_options.' + platform.platform_id">
                            <div class="data-options-container">
                                <el-checkbox-group v-model="form.input_data_options[platform.platform_id]"
                                    class="checkbox-group">
                                    <el-checkbox v-for="option in platform.options" :key="option.option_key"
                                        :label="option.option_key" class="checkbox-item">
                                        {{ option.option_name }}
                                    </el-checkbox>
                                </el-checkbox-group>
                                <el-button type="primary" size="small" icon="el-icon-plus"
                                    @click="showAddOptionDialog(platform.platform_id)">
                                    新增数据类型
                                </el-button>
                            </div>
                        </el-form-item>

                        <el-form-item label="效果参数配置">
                            <div class="effect-params-container">
                                <div v-if="getAvailableEffectParamsForPlatform(platform.platform_id).length === 0" class="no-effect-params-tip">
                                    <el-alert
                                        title="该平台暂无可用的效果参数"
                                        type="info"
                                        :closable="false"
                                        show-icon>
                                    </el-alert>
                                </div>
                                <el-checkbox-group v-else v-model="form.input_effect_params[platform.platform_id]" @change="handleEffectParamsChange(platform.platform_id)">
                                    <el-checkbox v-for="param in getAvailableEffectParamsForPlatform(platform.platform_id)" :key="param.effect_param_code" :label="param.effect_param_code" class="effect-param-checkbox">
                                        {{ param.effect_param_name }}
                                    </el-checkbox>
                                </el-checkbox-group>

                                <div v-if="form.input_effect_params[platform.platform_id] && form.input_effect_params[platform.platform_id].length > 0" class="effect-params-table">
                                    <h4>参数配置详情</h4>
                                    <el-table :data="getEffectParamsTableData(platform.platform_id)" border>
                                        <el-table-column prop="effect_param_name" label="参数名称" width="120"></el-table-column>
                                        <el-table-column prop="configured_evaluation_days" width="200">
                                            <template slot="header">
                                                <el-tooltip effect="dark" placement="top" content="系统会获取发布时间在'T-基线'范围内，且已满足各参数的Tij 值的样本总数量。">
                                                    <span class="table-header-with-tooltip">
                                                        *效果实现天数
                                                        <i class="el-icon-question"></i>
                                                    </span>
                                                </el-tooltip>
                                            </template>
                                            <template slot-scope="scope">
                                                <el-form-item
                                                    :prop="`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.configured_evaluation_days`"
                                                    style="margin-bottom: 0;">
                                                    <el-input
                                                        v-model="scope.row.configured_evaluation_days"
                                                        placeholder="如：3,5,10"
                                                        @change="updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'configured_evaluation_days', scope.row.configured_evaluation_days)">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="default_baseline_mean" width="200">
                                            <template slot="header">
                                                <el-tooltip effect="dark" placement="top" content="μi是选取的样本量的效果数据里，该参数的平均值。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算μi，而是会使用您输入的缺省状态下的μi。">
                                                    <span class="table-header-with-tooltip">
                                                        *平均值
                                                        <i class="el-icon-question"></i>
                                                    </span>
                                                </el-tooltip>
                                            </template>
                                            <template slot-scope="scope">
                                                <el-form-item
                                                    :prop="`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_mean`"
                                                    style="margin-bottom: 0;">
                                                    <el-input
                                                        v-model="scope.row.default_baseline_mean"
                                                        placeholder="如：0"
                                                        @change="updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_mean', scope.row.default_baseline_mean)">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="default_baseline_stddev" width="200">
                                            <template slot="header">
                                                <el-tooltip effect="dark" placement="top" content="σi是选取的样本量的效果数据里，该参数的标准差。当样本量少于您设置的最低样本量的阈值时，将不会根据实际样本去计算σi，而是会使用您输入的缺省状态下的σi。">
                                                    <span class="table-header-with-tooltip">
                                                        *标准差
                                                        <i class="el-icon-question"></i>
                                                    </span>
                                                </el-tooltip>
                                            </template>
                                            <template slot-scope="scope">
                                                <el-form-item
                                                    :prop="`input_effect_params_config.${platform.platform_id}.${scope.row.effect_param_code}.default_baseline_stddev`"
                                                    style="margin-bottom: 0;">
                                                    <el-input
                                                        v-model="scope.row.default_baseline_stddev"
                                                        placeholder="如：1"
                                                        @change="updateEffectParamConfig(platform.platform_id, scope.row.effect_param_code, 'default_baseline_stddev', scope.row.default_baseline_stddev)">
                                                    </el-input>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </el-form-item>

                        <el-form-item label="补充信息"
                            :prop="'input_platforms_data.' + platform.platform_id + '.additional_Information'">
                            <el-input v-model="form.input_platforms_data[platform.platform_id].additional_Information"
                                type="textarea" :rows="5" placeholder="请输入补充信息">
                            </el-input>
                        </el-form-item>
                    </el-card>
                </div>
            </div>

            <div v-show="activeStep === 3">
                <el-form-item label="选择输出平台" prop="output_platforms">
                    <div v-if="!form.linked_task_type_code" class="platform-selection-tip">
                        <el-alert
                            title="请先在第一步选择任务类型"
                            type="info"
                            :closable="false"
                            show-icon>
                        </el-alert>
                    </div>
                    <div v-else class="platform-selection-container">
                        <el-checkbox-group v-model="form.output_platforms" @change="handleOutputPlatformChange"
                            class="platform-checkbox-group">
                            <el-checkbox v-for="platform in availablePlatforms" :key="platform.platform_id"
                                :label="platform.platform_id" class="platform-checkbox-item">
                                {{ platform.platform_name }}
                            </el-checkbox>
                        </el-checkbox-group>
                        <div v-if="loadingOutputPlatforms" class="loading-tip">
                            <el-alert
                                title="正在加载平台配置，请稍候..."
                                type="info"
                                :closable="false"
                                show-icon>
                            </el-alert>
                        </div>
                        <div v-if="availablePlatforms.length === 0" class="no-platforms-tip">
                            <el-alert
                                title="当前任务类型没有关联的平台"
                                type="warning"
                                :closable="false"
                                show-icon>
                            </el-alert>
                        </div>
                    </div>
                </el-form-item>

                <div v-if="selectedOutputPlatforms && selectedOutputPlatforms.length > 0" class="platform-configs" v-loading="loadingOutputPlatforms" element-loading-text="正在加载平台配置...">
                    <h3>输出平台配置</h3>
                    <el-card v-for="platform in selectedOutputPlatforms" :key="platform.platform_id"
                        class="platform-card">
                        <div slot="header">
                            <span>{{ platform.platform_name }}</span>
                        </div>
                        {{form.output_platforms_data}}
                        <el-form-item v-for="field in platform.fields" :key="field.field_name" :label="field.label"
                            :prop="'output_platforms_data.' + platform.platform_id + '.' + field.field_name">
                            <component :is="getFieldComponent(field.field_type)"
                                v-model="form.output_platforms_data[platform.platform_id][field.field_name]"
                                v-bind="getFieldProps(field)"></component>
                        </el-form-item>

                        <el-form-item label="数据内容" :prop="'output_data_options.' + platform.platform_id">
                            <div class="data-options-container">
                                <el-checkbox-group v-model="form.output_data_options[platform.platform_id]"
                                    class="checkbox-group">
                                    <el-checkbox v-for="option in platform.options" :key="option.option_key"
                                        :label="option.option_key" class="checkbox-item">
                                        {{ option.option_name }}
                                    </el-checkbox>
                                </el-checkbox-group>
                                <el-button type="primary" size="small" icon="el-icon-plus"
                                    @click="showAddOptionDialog(platform.platform_id)">
                                    新增数据内容
                                </el-button>
                            </div>
                        </el-form-item>

                      <el-form-item label="模态" prop="'input_platforms_data.' + platform.platform_id + '.multimodal_array'">
                        <el-select v-model="form.output_platforms_data[platform.platform_id].multimodal_array" multiple placeholder="请选择模态">
                          <el-option
                              v-for="item in modalityOptions"
                              :key="item.dict_name"
                              :label="item.dict_name"
                              :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      {{form.input_platforms_data}}---
                      <el-form-item label="型态" prop="'input_platforms_data.' + platform.platform_id + '.morph_type'">
                        <el-select v-model="form.output_platforms_data[platform.platform_id].morph_type" multiple placeholder="请选择模态">
                          <el-option
                              v-for="item in morphTypes"
                              :key="item.ID"
                              :label="item.morph_name"
                              :value="item.ID">
                          </el-option>
                        </el-select>
                      </el-form-item>

                        <el-form-item label="补充信息"
                            :prop="'output_platforms_data.' + platform.platform_id + '.additional_Information'">
                            <el-input v-model="form.output_platforms_data[platform.platform_id].additional_Information"
                                type="textarea" :rows="5" placeholder="请输入补充信息">
                            </el-input>
                        </el-form-item>
                    </el-card>
                </div>


            </div>

            <div v-show="activeStep === 4">
                <el-form-item label="运行频率" prop="scene_running_frequency">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <el-input-number
                            v-model="frequencyValue"
                            :min="getMinValue()"
                            :max="getMaxValue()"
                            :step="getStep()"
                            placeholder="请输入数值"
                            style="width: 150px"
                            @change="handleFrequencyValueChange">
                        </el-input-number>
                        <el-select
                            v-model="frequencyUnit"
                            placeholder="请选择单位"
                            style="width: 120px"
                            @change="handleFrequencyUnitChange">
                            <el-option label="分钟" value="minutes"></el-option>
                            <el-option label="小时" value="hours"></el-option>
                            <el-option label="天" value="days"></el-option>
                        </el-select>
                        <span style="color: #909399; font-size: 12px;">
                            (最小间隔30分钟)
                        </span>
                    </div>
                </el-form-item>
                <!-- <el-form-item label="运行时间" prop="time_config">
                    <el-row>
                        <el-col :span="8">
                            <el-time-picker
                                v-model="form.hour"
                                format="HH:00"
                                :picker-options="{
                                    selectableRange: '00:00:00 - 23:00:00',
                                    format: 'HH:00'
                                }"
                                placeholder="请选择运行时间"
                                style="width: 100%">
                            </el-time-picker>
                        </el-col>
                        <el-col :span="8" v-if="form.scene_running_frequency === '每月一次'">
                            <el-select v-model="form.day" placeholder="请选择运行日期" style="width: 100%">
                                <el-option
                                    v-for="day in 31"
                                    :key="day"
                                    :label="`${day}日`"
                                    :value="day">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="8" v-if="['每周一次', '每两周一次'].includes(form.scene_running_frequency)">
                            <el-select v-model="form.weeks" placeholder="请选择运行星期" style="width: 100%">
                                <el-option label="星期一" value="1"></el-option>
                                <el-option label="星期二" value="2"></el-option>
                                <el-option label="星期三" value="3"></el-option>
                                <el-option label="星期四" value="4"></el-option>
                                <el-option label="星期五" value="5"></el-option>
                                <el-option label="星期六" value="6"></el-option>
                                <el-option label="星期日" value="0"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </el-form-item> -->
                <el-form-item label="个性化进化更新频率" prop="stored_strategy_refresh_days">
                    <el-input-number
                        v-model="form.stored_strategy_refresh_days"
                        :min="0"
                        :max="365"
                        :step="1"
                        placeholder="请输入天数"
                        style="width: 200px">
                    </el-input-number>
                    <span style="margin-left: 8px; color: #909399;">天</span>
                    <span style="margin-left: 16px; color: #909399; font-size: 12px;">建议您设为0</span>
                </el-form-item>
                <el-form-item label="AI自行探索频率" prop="explore_strategy_trigger_days">
                    <el-input-number
                        v-model="form.explore_strategy_trigger_days"
                        :min="1"
                        :max="365"
                        :step="1"
                        placeholder="请输入天数"
                        style="width: 200px">
                    </el-input-number>
                    <span style="margin-left: 8px; color: #909399;">天</span>
                    <span style="margin-left: 16px; color: #909399; font-size: 12px;">目前我们的探索模式暂未上线，建议您先将Z值设为365天</span>
                </el-form-item>
                <el-form-item label="AI提示词" prop="updated_prompt">
                    <el-input type="textarea" v-model="form.updated_prompt" placeholder="请输入AI提示词" :rows="10"></el-input>
                </el-form-item>
            </div>

            <el-form-item class="navigation-buttons">
                <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
                <el-button
                    v-if="activeStep < 4"
                    type="primary"
                    @click="nextStep"
                    :disabled="isPlatformConfigLoading"
                    :loading="isPlatformConfigLoading">
                    {{ isPlatformConfigLoading ? '配置加载中...' : '下一步' }}
                </el-button>
                <el-button v-if="activeStep === 4" type="primary" @click="submitForm">保存</el-button>
            </el-form-item>
        </el-form>

        <el-dialog title="新增数据内容" :visible.sync="addOptionDialogVisible" width="500px">
            <el-form ref="optionForm" :model="newOptionForm" :rules="optionRules" label-width="120px">
                <el-form-item label="内容名称" prop="option_name">
                    <el-input v-model="newOptionForm.option_name" placeholder="请输入内容名称"></el-input>
                </el-form-item>
                <el-form-item label="内容标识" prop="option_key">
                    <el-input v-model="newOptionForm.option_key" placeholder="请输入内容标识（英文）"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addOptionDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="addNewOption" :loading="addingOption">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="新增项目组" :visible.sync="addProjectDialogVisible" width="500px">
            <el-form ref="projectForm" :model="newProjectForm" :rules="projectRules" label-width="120px">
                <el-form-item label="项目名称" prop="project_name">
                    <el-input v-model="newProjectForm.project_name" placeholder="请输入项目名称"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addProjectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="addNewProject" :loading="addingProject">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="新增任务类型" :visible.sync="addTaskTypeDialogVisible" width="600px">
            <el-form ref="taskTypeForm" :model="newTaskTypeForm" :rules="taskTypeRules" label-width="140px">
                <el-form-item label="任务类型名称" prop="task_type_name">
                    <el-input v-model="newTaskTypeForm.task_type_name" placeholder="请输入任务类型名称"></el-input>
                </el-form-item>
                <el-form-item label="任务类型描述" prop="task_type_description">
                    <el-input type="textarea" v-model="newTaskTypeForm.task_type_description" placeholder="请输入任务类型描述" :rows="3"></el-input>
                </el-form-item>
                <el-form-item label="关联平台" prop="linked_platform_ids">
                    <el-select
                        v-model="selectedPlatformIds"
                        multiple
                        placeholder="请选择关联平台"
                        style="width: 100%"
                        @change="handlePlatformSelectChange">
                        <el-option
                            v-for="platform in platforms"
                            :key="platform.platform_id"
                            :label="platform.platform_name"
                            :value="platform.platform_id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="推荐效果参数" prop="recommended_effect_param_codes">
                    <el-select
                        v-model="selectedEffectParams"
                        multiple
                        placeholder="请选择推荐效果参数"
                        style="width: 100%"
                        @change="handleEffectParamsSelectChange">
                        <el-option
                            v-for="param in availableEffectParamsForTaskType"
                            :key="param.effect_param_code"
                            :label="`${param.effect_param_name} (${param.effect_param_code})`"
                            :value="param.effect_param_name">
                        </el-option>
                    </el-select>
                    <div v-if="availableEffectParamsForTaskType.length === 0 && selectedPlatformIds.length > 0" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        所选平台暂无可用的效果参数
                    </div>
                    <div v-if="selectedPlatformIds.length === 0" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        请先选择关联平台
                    </div>
                </el-form-item>
                <el-form-item label="参数关系说明" prop="effect_param_relationships_note">
                    <el-input type="textarea" v-model="newTaskTypeForm.effect_param_relationships_note" placeholder="请输入各推荐参数之间的逻辑关系说明" :rows="3"></el-input>
                </el-form-item>
                <!-- <el-form-item label="是否需要外部内容" prop="is_content_from_external">
                    <el-select v-model="newTaskTypeForm.is_content_from_external" placeholder="请选择">
                        <el-option label="是" value="1"></el-option>
                        <el-option label="否" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否需要双边偏好" prop="is_bilateral_pref_consideration_needed">
                    <el-select v-model="newTaskTypeForm.is_bilateral_pref_consideration_needed" placeholder="请选择">
                        <el-option label="是" value="1"></el-option>
                        <el-option label="否" value="0"></el-option>
                    </el-select>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addTaskTypeDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="addNewTaskType" :loading="addingTaskType">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
    name: 'EditScene',
    data() {
        return {
            activeStep: 0,
            loading: false,
            form: {
                scene_id: null,
                linked_project_team_name: null,
                linked_task_type_code: null,
                scene_name: '',
                scene_description: '',
                input_platforms: [],
                output_platforms: [],
                input_platforms_data: {},
                output_platforms_data: {},
                input_data_options: {},
                output_data_options: {},
                input_effect_params: {},
                input_effect_params_config: {},
                updated_prompt: '',
                scene_running_frequency: '',
                hour: '',
                modality: [],
                day: '',
                weeks: '',
                stored_strategy_refresh_days: 0,
                explore_strategy_trigger_days: 365,
                scene_business_type: '',
                baseline_data_start_days_ago: 30,
                baseline_data_exclude_recent_days: 3,
                min_baseline_sample_count: 3,
                baseline_refresh_frequency_days: 7
            },
            frequencyValue: 30,
            frequencyUnit: 'minutes',
            rules: {
                linked_project_team_name: [
                    {
                        required: true,
                        message: '请选择项目组',
                        trigger: ['change', 'blur'],
                        validator: (rule, value, callback) => {
                            console.log('验证项目组:', value)
                            if (!value) {
                                callback(new Error('请选择项目组'))
                            } else {
                                callback()
                            }
                        }
                    }
                ],
                linked_task_type_code: [
                    {
                        required: true,
                        message: '请选择任务类型',
                        trigger: ['change', 'blur'],
                        validator: (rule, value, callback) => {
                            console.log('验证任务类型:', value)
                            if (!value) {
                                callback(new Error('请选择任务类型'))
                            } else {
                                callback()
                            }
                        }
                    }
                ],
                scene_name: [
                    { required: true, message: '请输入场景名称', trigger: 'blur' }
                ],
                scene_description: [
                    { required: false, message: '请输入场景描述', trigger: 'blur' }
                ],
                input_platforms: [
                    { required: true, message: '请选择输入平台', trigger: 'change' }
                ],
                output_platforms: [
                    { required: true, message: '请选择输出平台', trigger: 'change' }
                ],
                updated_prompt: [
                    { required: true, message: '请输入AI提示词', trigger: 'blur' }
                ],
                scene_running_frequency: [
                    { required: true, message: '请设置运行频率', trigger: 'change' },
                    {
                        type: 'number',
                        min: 30,
                        message: '运行频率最小间隔为30分钟',
                        trigger: 'change'
                    }
                ],
                stored_strategy_refresh_days: [
                    { required: true, message: '请输入个性化进化策略更新频率', trigger: 'blur' },
                    { type: 'number', min: 0, max: 365, message: '请输入0-365之间的天数', trigger: 'blur' }
                ],
                explore_strategy_trigger_days: [
                    { required: true, message: '请输入AI自行探索频率', trigger: 'blur' },
                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }
                ],
                scene_business_type: [
                    { required: true, message: '请选择场景类型', trigger: 'change' }
                ],
                baseline_data_start_days_ago: [
                    { required: true, message: '请输入使用数据的起始天数', trigger: 'blur' },
                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }
                ],
                baseline_data_exclude_recent_days: [
                    { required: true, message: '请输入排除最近的数据天数', trigger: 'blur' },
                    { type: 'number', min: 0, max: 365, message: '请输入0-30之间的天数', trigger: 'blur' }
                ],
                min_baseline_sample_count: [
                    { required: true, message: '请输入样本量最小阈值', trigger: 'blur' },
                    { type: 'number', min: 1, max: 10000, message: '请输入1-10000之间的数值', trigger: 'blur' }
                ],
                baseline_refresh_frequency_days: [
                    { required: true, message: '请输入基准线更新频率', trigger: 'blur' },
                    { type: 'number', min: 1, max: 365, message: '请输入1-365之间的天数', trigger: 'blur' }
                ]
            },
            projectTeams: [],
            taskTypes: [],
            modalityOptions: [],
            selectedInputPlatforms: [],
            selectedOutputPlatforms: [],
            sceneInputAccountsData: {},
            sceneOutputAccountsData: {},
            effectParamsData: {},
            addOptionDialogVisible: false,
            newOptionForm: {
                option_name: '',
                option_key: '',
                platform_id: ''
            },
            optionRules: {
                option_name: [
                    { required: true, message: '请输入内容名称', trigger: 'blur' }
                ],
                option_key: [
                    { required: true, message: '请输入内容标识（英文）', trigger: 'blur' }
                ]
            },
            addingOption: false,
            addProjectDialogVisible: false,
            addTaskTypeDialogVisible: false,
            newProjectForm: {
                project_name: ''
            },
            newTaskTypeForm: {
                task_type_name: '',
                task_type_description: '',
                recommended_effect_param_codes: '',
                effect_param_relationships_note: '',
                is_content_from_external: '1',
                is_bilateral_pref_consideration_needed: '0',
                linked_platform_ids: '',
                task_type_status: 1,
                task_type_owner: 2
            },
            selectedPlatformIds: [],
            selectedEffectParams: [],
            availableEffectParamsForTaskType: [],
            projectRules: {
                project_name: [
                    { required: true, message: '请输入项目名称', trigger: 'blur' }
                ]
            },
            taskTypeRules: {
                task_type_name: [
                    { required: true, message: '请输入任务类型名称', trigger: 'blur' }
                ],
                task_type_description: [
                    { required: true, message: '请输入任务类型描述', trigger: 'blur' }
                ],
                recommended_effect_param_codes: [
                    { required: true, message: '请输入推荐效果参数编码', trigger: 'blur' }
                ],
                effect_param_relationships_note: [
                    { required: true, message: '请输入各推荐参数之间的逻辑关系说明', trigger: 'blur' }
                ]
            },
            addingProject: false,
            addingTaskType: false,
            sceneTypes: [],
            loadingInputPlatforms: false,
            loadingOutputPlatforms: false
        }
    },
    computed: {
        ...mapState(['platforms','morphTypes']),
        availablePlatforms() {
            if (!this.platforms || !this.form.linked_task_type_code) {
                return this.platforms
            }

            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)
            if (!selectedTaskType || !selectedTaskType.linked_platform_ids) {
                return this.platforms
            }

            const linkedPlatformIds = selectedTaskType.linked_platform_ids.split(',').map(id => parseInt(id.trim()))
            return this.platforms.filter(platform => linkedPlatformIds.includes(platform.platform_id))
        },
        availableEffectParams() {
            if (!this.form.linked_task_type_code|| !this.taskTypes) {
                return []
            }

            const selectedTaskType = this.taskTypes.find(taskType => taskType.task_type_code === this.form.linked_task_type_code)
            if (!selectedTaskType || !selectedTaskType.recommended_effect_param_codes) {
                return []
            }

            try {
                const params = JSON.parse(selectedTaskType.recommended_effect_param_codes)
                return Array.isArray(params) ? params : []
            } catch (error) {
                console.error('解析效果参数失败:', error)
                return []
            }
        },
        isPlatformConfigLoading() {
            if (this.activeStep === 2 && this.loadingInputPlatforms) {
                return true
            }
            if (this.activeStep === 3 && this.loadingOutputPlatforms) {
                return true
            }
            return false
        }
    },
    methods: {
        ...mapActions(['fetchPlatforms','fetchMorphTypes']),
        async fetchPlatformOptions(platformId) {
            try {
                const response = await this.$http.post('', {
                    api: '/api/option/getList',
                    param: {
                        platform_id: platformId
                    }
                })
                return response.data.data || []
            } catch (error) {
                console.error('Error fetching platform options:', error)
                return []
            }
        },
        async fetchPlatformEffectParams(platformId) {
            try {
                const response = await this.$http.post('', {
                    api: '/api/effectParamCategory/getList',
                    param: {
                        platform_id: platformId
                    }
                })
                return response.data.data || []
            } catch (error) {
                console.error('Error fetching platform effect params:', error)
                return []
            }
        },
        getAvailableEffectParamsForPlatform(platformId) {
            const recommendedParams = this.availableEffectParams || []

            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)
            const platformParams = platform ? (platform.effectParams || []) : []

            console.info(platformParams)

            if (platformParams.length === 0) {
                return []
            }

            // const platformParamCodes = platformParams.map(param => param.effect_param_name || param.effect_param_code)

            return platformParams.filter(param => recommendedParams.includes(param.effect_param_name))
        },
        getFieldComponent(type) {
            const componentMap = {
                'string': 'el-input',
                'password': 'el-input',
                'select': 'el-select',
                'multiselect': 'el-select',
                'number': 'el-input-number',
                'bool': 'el-switch',
                'textarea': 'el-input'
            }
            return componentMap[type] || 'el-input'
        },
        getFieldProps(field) {
            const props = {
                placeholder: `请输入${field.label}`
            }
            if (field.field_type === 'password') {
                props.type = 'password'
            }
            if (field.field_type === 'textarea') {
                props.type = 'textarea'
                props.rows = 3
            }
            if (field.field_type === 'select' || field.field_type === 'multiselect') {
                props.multiple = field.field_type === 'multiselect'
                props.options = field.options || []
            }
            return props
        },
        async handleInputPlatformChange(platformIds) {
            if (this.loadingInputPlatforms) {
                return
            }
            this.loadingInputPlatforms = true

            try {
                this.selectedInputPlatforms = []

                Object.keys(this.rules).forEach(key => {
                    if (key.startsWith('input_platforms_data.') && !key.endsWith('.additional_Information')) {
                        this.$delete(this.rules, key)
                    }
                })

                const platformsWithDetails = []
                for (const platformId of platformIds) {
                    const platform = this.platforms.find(p => p.platform_id === platformId)
                    if (!platform) continue

                    try {
                        const detailResponse = await this.$http.post('', {
                            api: '/api/platform/getDetail',
                            param: { platform_id: platformId }
                        })

                        const options = await this.fetchPlatformOptions(platformId)

                        const effectParams = await this.fetchPlatformEffectParams(platformId)

                        console.info("effectParams", effectParams);

                        platformsWithDetails.push({
                            ...detailResponse.data.data,
                            options: options,
                            effectParams: effectParams
                        })
                    } catch (error) {
                        console.error('Error fetching platform detail:', error)
                        this.$message.error(`获取平台${platform.platform_name}详情失败`)
                    }
                }

                this.selectedInputPlatforms = platformsWithDetails

                for (const platformId of platformIds) {

                        if (!this.form.input_platforms_data[platformId]) {
                            this.$set(this.form.input_platforms_data, platformId, {
                                additional_Information: ''
                            })
                        }

                        if (!this.form.input_data_options[platformId]) {
                            this.$set(this.form.input_data_options, platformId, [])
                        }

                        if (!this.form.input_effect_params[platformId]) {
                            this.$set(this.form.input_effect_params, platformId, [])
                        }

                        const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)
                        if (platformWithDetails && platformWithDetails.fields) {
                            platformWithDetails.fields.forEach(field => {
                                if (field.required) {
                                    const fieldProp = `input_platforms_data.${platformId}.${field.field_name}`
                                    this.$set(this.rules, fieldProp, [
                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
                                    ])
                                }
                            })
                        }

                        if (this.sceneInputAccountsData[platformId]) {
                            const accountData = this.sceneInputAccountsData[platformId]
                            const platformWithDetails = this.selectedInputPlatforms.find(p => p.platform_id === platformId)

                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {
                                if (accountData[field.field_name] !== undefined) {
                                    this.$set(this.form.input_platforms_data[platformId], field.field_name, accountData[field.field_name])
                                }
                            }

                            if (accountData.additional_Information !== undefined) {
                                this.$set(this.form.input_platforms_data[platformId], 'additional_Information', accountData.additional_Information)
                            }

                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {
                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())
                                this.$set(this.form.input_data_options, platformId, selectedOptions)
                            }
                        }

                        if (this.effectParamsData) {
                            const platformEffectParams = Object.values(this.effectParamsData).filter(param => param.platform_id === platformId)

                            if (platformEffectParams.length > 0) {
                                if (!this.form.input_effect_params[platformId]) {
                                    this.$set(this.form.input_effect_params, platformId, [])
                                }

                                if (!this.form.input_effect_params_config[platformId]) {
                                    this.$set(this.form.input_effect_params_config, platformId, {})
                                }

                                const selectedParams = []
                                platformEffectParams.forEach(param => {
                                    selectedParams.push(param.effect_param_code)
                                    this.$set(this.form.input_effect_params_config[platformId], param.effect_param_code, {
                                        effect_param_code: param.effect_param_code,
                                        effect_param_name: param.effect_param_name,
                                        configured_evaluation_days: param.configured_evaluation_days || '',
                                        default_baseline_mean: param.default_baseline_mean || '',
                                        default_baseline_stddev: param.default_baseline_stddev || ''
                                    })
                                })

                                this.$set(this.form.input_effect_params, platformId, selectedParams)
                            }
                        }
                }
            } catch (error) {
                console.error('Error in handleInputPlatformChange:', error)
                this.$message.error('处理输入平台变化时出错')
            } finally {
                this.loadingInputPlatforms = false
            }
        },
        async handleOutputPlatformChange(platformIds) {
            if (this.loadingOutputPlatforms) {
                return
            }
            this.loadingOutputPlatforms = true

            try {
                this.selectedOutputPlatforms = []

                Object.keys(this.rules).forEach(key => {
                    if (key.startsWith('output_platforms_data.') && !key.endsWith('.additional_Information')) {
                        this.$delete(this.rules, key)
                    }
                })

                const platformsWithDetails = []
                for (const platformId of platformIds) {
                    const platform = this.platforms.find(p => p.platform_id === platformId)
                    if (!platform) continue

                    try {
                        const detailResponse = await this.$http.post('', {
                            api: '/api/platform/getDetail',
                            param: { platform_id: platformId }
                        })

                        const options = await this.fetchPlatformOptions(platformId)

                        platformsWithDetails.push({
                            ...detailResponse.data.data,
                            options: options
                        })
                    } catch (error) {
                        console.error('Error fetching platform detail:', error)
                        this.$message.error(`获取平台${platform.platform_name}详情失败`)
                    }
                }

                this.selectedOutputPlatforms = platformsWithDetails

                for (const platformId of platformIds) {

                        if (!this.form.output_platforms_data[platformId]) {
                            this.$set(this.form.output_platforms_data, platformId, {
                                additional_Information: ''
                            })
                        }

                        if (!this.form.output_data_options[platformId]) {
                            this.$set(this.form.output_data_options, platformId, [])
                        }

                        const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)
                        if (platformWithDetails && platformWithDetails.fields) {
                            platformWithDetails.fields.forEach(field => {
                                if (field.required) {
                                    const fieldProp = `output_platforms_data.${platformId}.${field.field_name}`
                                    this.$set(this.rules, fieldProp, [
                                        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
                                    ])
                                }
                            })
                        }

                        if (this.sceneOutputAccountsData[platformId]) {
                            const accountData = this.sceneOutputAccountsData[platformId]
                            const platformWithDetails = this.selectedOutputPlatforms.find(p => p.platform_id === platformId)

                            for (const field of (platformWithDetails && platformWithDetails.fields) || []) {
                                if (accountData[field.field_name] !== undefined) {
                                    this.$set(this.form.output_platforms_data[platformId], field.field_name, accountData[field.field_name])
                                }
                            }

                            if (accountData.additional_Information !== undefined) {
                                this.$set(this.form.output_platforms_data[platformId], 'additional_Information', accountData.additional_Information)
                            }

                            if (accountData.adding_data_types && accountData.adding_data_types !== '无') {
                                const selectedOptions = accountData.adding_data_types.split(',').filter(item => item.trim())
                                this.$set(this.form.output_data_options, platformId, selectedOptions)
                            }
                        }
                }
            } catch (error) {
                console.error('Error in handleOutputPlatformChange:', error)
                this.$message.error('处理输出平台变化时出错')
            } finally {
                this.loadingOutputPlatforms = false
            }
        },
        nextStep() {
            if (this.isPlatformConfigLoading) {
                this.$message.warning('平台配置正在加载中，请稍候...')
                return
            }

            let fieldsToValidate = []

            switch (this.activeStep) {
                case 0:
                    fieldsToValidate = ['linked_project_team_name', 'linked_task_type_code', 'scene_business_type', 'scene_name', 'scene_description']
                    break
                case 1:
                    fieldsToValidate = ['baseline_data_start_days_ago', 'baseline_data_exclude_recent_days', 'min_baseline_sample_count', 'baseline_refresh_frequency_days']
                    break
                case 2:
                    fieldsToValidate = ['input_platforms']

                    this.selectedInputPlatforms.forEach(platform => {
                        if (platform.fields) {
                            platform.fields.forEach(field => {
                                if (field.required) {
                                    const fieldProp = `input_platforms_data.${platform.platform_id}.${field.field_name}`
                                    fieldsToValidate.push(fieldProp)
                                }
                            })
                        }

                        const selectedParams = this.form.input_effect_params[platform.platform_id] || []
                        selectedParams.forEach(paramCode => {
                            const configPrefix = `input_effect_params_config.${platform.platform_id}.${paramCode}`
                            fieldsToValidate.push(`${configPrefix}.configured_evaluation_days`)
                            fieldsToValidate.push(`${configPrefix}.default_baseline_mean`)
                            fieldsToValidate.push(`${configPrefix}.default_baseline_stddev`)
                        })
                    })
                    break
                case 3:
                    fieldsToValidate = ['output_platforms']

                    this.selectedOutputPlatforms.forEach(platform => {
                        if (platform.fields) {
                            platform.fields.forEach(field => {
                                if (field.required) {
                                    const fieldProp = `output_platforms_data.${platform.platform_id}.${field.field_name}`
                                    fieldsToValidate.push(fieldProp)
                                }
                            })
                        }
                    })
                    break
                case 4:
                    fieldsToValidate = ['updated_prompt', 'scene_running_frequency', 'stored_strategy_refresh_days', 'explore_strategy_trigger_days']
                    break
                default:
                    break
            }

            this.validateFields(fieldsToValidate, (valid) => {
                if (valid) {
                    this.activeStep++
                }
            })
        },
        validateFields(fields, callback) {
            if (fields.length === 0) {
                callback(true)
                return
            }

            const validationPromises = fields.map(field => {
                return new Promise((resolve) => {
                    this.$refs.form.validateField(field, (errorMessage) => {
                        console.log(`字段 ${field} 验证结果:`, errorMessage)
                        resolve({ field, errorMessage })
                    })
                })
            })

            Promise.all(validationPromises).then(results => {
                const hasError = results.some(result => result.errorMessage)
                console.log('验证结果:', results, '是否有错误:', hasError)
                callback(!hasError)
            })
        },
        prevStep() {
            this.activeStep--
        },
        async submitForm() {
            this.$refs.form.validate(async valid => {
                if (valid) {
                    try {
                        const accounts = []
                        const effectParams = []

                        for (const platformId of this.form.input_platforms) {
                            const platformData = this.form.input_platforms_data[platformId] || {}
                            const dataOptions = this.form.input_data_options[platformId] || []

                            accounts.push({
                                operate_type: 1,
                                platform_id: platformId,
                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),
                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',
                                ...platformData
                            })

                            if (this.form.input_effect_params_config[platformId]) {
                                Object.values(this.form.input_effect_params_config[platformId]).forEach(paramConfig => {
                                    effectParams.push({
                                        platform_id: platformId,
                                        ...paramConfig
                                    })
                                })
                            }
                        }

                        for (const platformId of this.form.output_platforms) {
                            const platformData = this.form.output_platforms_data[platformId] || {}
                            const dataOptions = this.form.output_data_options[platformId] || []

                            accounts.push({
                                operate_type: 2,
                                platform_id: platformId,
                                create_time: new Date().toLocaleString('sv-SE').replace('T', ' '),
                                adding_data_types: dataOptions.length > 0 ? dataOptions.join(',') : '无',
                                ...platformData
                            })
                        }

                        const { input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config, ...submitData } = this.form;
                        console.info(input_platforms, input_data_options, input_platforms_data, output_data_options, output_platforms, output_platforms_data, input_effect_params, output_effect_params, input_effect_params_config, output_effect_params_config)

                        // 格式化模态数据为字符串格式用于提交
                        submitData.modality = this.formatModalityForSubmit(this.form.modality);
                        submitData.update_time = new Date().toLocaleString('sv-SE').replace('T', ' ');

                        await this.$http.post('', {
                            api: '/api/scene/update',
                            data: {
                                ...submitData,
                                scene_id: this.$route.params.id
                            },
                            accounts: accounts,
                            effect_params: effectParams
                        })
                        this.$message.success('场景更新成功')
                        this.$router.push('/')
                    } catch (error) {
                        this.$message.error('场景更新失败')
                        console.error('Error updating scene:', error)
                    }
                }
            })
        },
        async fetchSceneDetail() {
            if (!this.$route.params.id) {
                return
            }

            this.loading = true
            try {
                const response = await this.$http.post('', {
                    api: '/api/scene/getDetail',
                    param: { scene_id: this.$route.params.id }
                })
                const sceneData = response.data.data
                const accounts = response.data.accounts || []
                const effectParams = response.data.effect_params || []

                this.sceneInputAccountsData = {}
                this.sceneOutputAccountsData = {}
                accounts.forEach(account => {
                    if (account.operate_type === 1) {
                        this.sceneInputAccountsData[account.platform_id] = account
                    } else if (account.operate_type === 2) {
                        this.sceneOutputAccountsData[account.platform_id] = account
                    }

                })
                console.log('accounts',accounts)
                const inputPlatforms = accounts
                    .filter(platform => platform.operate_type === 1)
                    .map(platform => platform.platform_id)

                const outputPlatforms = accounts
                    .filter(platform => platform.operate_type === 2)
                    .map(platform => platform.platform_id)

                this.effectParamsData = {}
                effectParams.forEach(param => {
                    this.effectParamsData[param.effect_param_code] = param
                })

                console.info("effectParamsData", this.effectParamsData);

                this.form = {
                    ...this.form,
                    ...sceneData,
                    linked_task_type_code: parseInt(sceneData.linked_task_type_code),
                    input_platforms: inputPlatforms,
                    output_platforms: outputPlatforms,
                    // 处理模态字段的数据格式转换
                    modality: this.parseModalityData(sceneData.modality)
                }

                if (sceneData.scene_running_frequency) {
                    this.parseFrequencyFromMinutes(parseInt(sceneData.scene_running_frequency))
                } else {
                    this.frequencyValue = 30
                    this.frequencyUnit = 'minutes'
                }
                this.calculateTotalMinutes()

                await this.$nextTick()
                await this.handleInputPlatformChange(this.form.input_platforms)
                await this.handleOutputPlatformChange(this.form.output_platforms)

            } catch (error) {
                this.$message.error('获取场景详情失败')
                console.error('Error fetching scene detail:', error)
            } finally {
                this.loading = false
            }
        },
        showAddOptionDialog(platformId) {
            this.newOptionForm = {
                option_name: '',
                option_key: '',
                platform_id: platformId
            }
            this.addOptionDialogVisible = true
            this.$nextTick(() => {
                this.$refs.optionForm && this.$refs.optionForm.clearValidate()
            })
        },
        async addNewOption() {
            this.$refs.optionForm.validate(async valid => {
                if (valid) {
                    try {
                        this.addingOption = true
                        await this.$http.post('', {
                            api: '/api/option/add',
                            data: {
                                ...this.newOptionForm,
                                platform_id: this.newOptionForm.platform_id
                            }
                        })
                        this.$message.success('新增数据类型成功')
                        this.addOptionDialogVisible = false
                        this.newOptionForm = {
                            option_name: '',
                            option_key: '',
                            platform_id: ''
                        }
                        await this.handleInputPlatformChange(this.form.input_platforms)
                        await this.handleOutputPlatformChange(this.form.output_platforms)
                    } catch (error) {
                        this.$message.error('新增数据类型失败')
                        console.error('Error adding new option:', error)
                    } finally {
                        this.addingOption = false
                    }
                }
            })
        },
        openAIOptimize() {
            if (!this.form.scene_id) return
            const url = `https://acpfbbeg.manus.space/?scene_id=${this.form.scene_id}`
            window.open(url, '_blank')
        },
        async handleDeleteScene() {
            if (!this.form.scene_id) return
            try {
                await this.$confirm('确认删除该场景吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                await this.$http.post('', {
                    api: '/api/scene/delete',
                    param: { scene_id: this.form.scene_id }
                })
                this.$message.success('删除成功')
                this.$router.push('/scene/manage')
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除失败')
                    console.error('Error deleting scene:', error)
                }
            }
        },
        async fetchModalityOptions() {
            try {
                const response = await this.$http.post('', {
                    api: '/api/dict/getList',
                    param: {
                        dict_type: 'modality'
                    }
                })
                this.modalityOptions = response.data.data || []
            } catch (error) {
                console.error('Error fetching modality options:', error)
                this.$message.error('获取模态列表失败')
            }
        },
        // 处理模态数据格式转换
        parseModalityData(modalityData) {
            if (!modalityData) {
                return []
            }

            // 如果已经是数组，直接返回
            if (Array.isArray(modalityData)) {
                return modalityData
            }

            // 如果是字符串
            if (typeof modalityData === 'string') {
                // 尝试解析 JSON 格式的字符串
                if (modalityData.startsWith('[') && modalityData.endsWith(']')) {
                    try {
                        const parsed = JSON.parse(modalityData)
                        return Array.isArray(parsed) ? parsed : []
                    } catch (error) {
                        console.error('解析模态 JSON 数据失败:', error)
                    }
                }

                // 按逗号分割转换为数组
                return modalityData.split(',').map(item => item.trim()).filter(item => item)
            }

            return []
        },
        // 格式化模态数据用于提交
        formatModalityForSubmit(modalityArray) {
            if (!Array.isArray(modalityArray) || modalityArray.length === 0) {
                return ''
            }
            return modalityArray.join(',')
        },
        async fetchProjectTeams() {
            try {
                const response = await this.$http.post('', {
                    api: '/api/projectTeam/getList'
                })
                this.projectTeams = response.data.data || []
                console.log('项目组数据:', this.projectTeams)
            } catch (error) {
                console.error('Error fetching project teams:', error)
                this.$message.error('获取项目组列表失败')
            }
        },
        async fetchTaskTypes() {
            try {
                const response = await this.$http.post('', {
                    api: '/api/taskType/getList'
                })
                this.taskTypes = response.data.data || []
            } catch (error) {
                console.error('Error fetching task types:', error)
                this.$message.error('获取任务类型列表失败')
            }
        },
        async fetchSceneTypes() {
            try {
                const response = await this.$http.post('', {
                    api: '/api/sceneType/getList'
                })
                this.sceneTypes = response.data.data || []
            } catch (error) {
                console.error('Error fetching scene types:', error)
                this.$message.error('获取场景类型列表失败')
            }
        },
        handleProjectTeamChange() {
            console.log('项目组改变:', this.form.linked_project_team_name)
            this.$nextTick(() => {
                this.$refs.form.clearValidate('linked_project_team_name')
            })
        },
        handleTaskTypeChange() {
            this.form.input_platforms = []
            this.form.output_platforms = []
            this.selectedInputPlatforms = []
            this.selectedOutputPlatforms = []
            this.form.input_platforms_data = {}
            this.form.output_platforms_data = {}
            this.form.input_data_options = {}
            this.form.output_data_options = {}
            this.form.input_effect_params = {}
            this.form.input_effect_params_config = {}
            this.$nextTick(() => {
                this.$refs.form.clearValidate('linked_task_type_code')
            })
        },
        handleEffectParamsChange(platformId) {
            const selectedParams = this.form.input_effect_params[platformId] || []

            if (!this.form.input_effect_params_config[platformId]) {
                this.$set(this.form.input_effect_params_config, platformId, {})
            }

            const platform = this.selectedInputPlatforms.find(p => p.platform_id === platformId)
            const platformParams = platform ? (platform.effectParams || []) : []

            if (platformParams.length == 0) {
                return;
            }

            Object.keys(this.rules).forEach(key => {
                if (key.startsWith(`input_effect_params_config.${platformId}.`)) {
                    this.$delete(this.rules, key)
                }
            })

            selectedParams.forEach(param => {
                if (!this.form.input_effect_params_config[platformId][param]) {

                    const effectParam = platformParams.find(p => p.effect_param_code === param);

                    this.$set(this.form.input_effect_params_config[platformId], param, {
                        effect_param_code: effectParam.effect_param_code,
                        effect_param_name: effectParam.effect_param_name,
                        configured_evaluation_days: '',
                        default_baseline_mean: '',
                        default_baseline_stddev: ''
                    })
                }

                const configPrefix = `input_effect_params_config.${platformId}.${param}`
                this.$set(this.rules, `${configPrefix}.configured_evaluation_days`, [
                    { required: true, message: '请输入效果实现天数', trigger: 'blur' },
                    { pattern: /^[\d,\s]+$/, message: '请输入有效的天数格式，如：3,5,10', trigger: 'blur' }
                ])
                this.$set(this.rules, `${configPrefix}.default_baseline_mean`, [
                    { required: true, message: '请输入平均值', trigger: 'blur' },
                    { pattern: /^(-?[1-9]\d*(\.\d*[1-9])?)|(-?0\.\d*[1-9])$/, message: '平均值必须是数字', trigger: 'blur' }
                ])
                this.$set(this.rules, `${configPrefix}.default_baseline_stddev`, [
                    { required: true, message: '请输入标准差', trigger: 'blur' },
                    { pattern: /^(-?[1-9]\d*(\.\d*[1-9])?)|(-?0\.\d*[1-9])$/, message: '标准差必须是数字', trigger: 'blur' }
                ])
            })

            Object.keys(this.form.input_effect_params_config[platformId]).forEach(param => {
                if (!selectedParams.includes(param)) {
                    this.$delete(this.form.input_effect_params_config[platformId], param)
                }
            })
        },
        getEffectParamsTableData(platformId) {
            const selectedParams = this.form.input_effect_params[platformId] || []
            const config = this.form.input_effect_params_config[platformId] || {}

            const ret = selectedParams.map(paramCode => ({
                effect_param_code: paramCode,
                effect_param_name: (config[paramCode] && config[paramCode].effect_param_name) || '',
                configured_evaluation_days: (config[paramCode] && config[paramCode].configured_evaluation_days) || '',
                default_baseline_mean: (config[paramCode] && config[paramCode].default_baseline_mean) || 0,
                default_baseline_stddev: (config[paramCode] && config[paramCode].default_baseline_stddev) || 1
            }))

            console.info("ret", ret);
            return ret;
        },
        updateEffectParamConfig(platformId, paramName, field, value) {
            if (!this.form.input_effect_params_config[platformId]) {
                this.$set(this.form.input_effect_params_config, platformId, {})
            }
            if (!this.form.input_effect_params_config[platformId][paramName]) {
                this.$set(this.form.input_effect_params_config[platformId], paramName, {})
            }
            this.$set(this.form.input_effect_params_config[platformId][paramName], field, value)
        },
        getMinValue() {
            switch (this.frequencyUnit) {
                case 'minutes':
                    return 30
                case 'hours':
                    return 1
                case 'days':
                    return 1
                default:
                    return 1
            }
        },
        getMaxValue() {
            switch (this.frequencyUnit) {
                case 'minutes':
                    return 1440
                case 'hours':
                    return 24
                case 'days':
                    return 365
                default:
                    return 1
            }
        },
        getStep() {
            switch (this.frequencyUnit) {
                case 'minutes':
                    return 30
                case 'hours':
                    return 1
                case 'days':
                    return 1
                default:
                    return 1
            }
        },
        calculateTotalMinutes() {
            let totalMinutes = 0
            switch (this.frequencyUnit) {
                case 'minutes':
                    totalMinutes = this.frequencyValue
                    break
                case 'hours':
                    totalMinutes = this.frequencyValue * 60
                    break
                case 'days':
                    totalMinutes = this.frequencyValue * 24 * 60
                    break
            }

            if (totalMinutes < 30) {
                totalMinutes = 30
                this.frequencyValue = 30
                this.frequencyUnit = 'minutes'
            }

            this.form.scene_running_frequency = totalMinutes
            return totalMinutes
        },
        parseFrequencyFromMinutes(minutes) {
            if (!minutes || minutes < 30) {
                this.frequencyValue = 30
                this.frequencyUnit = 'minutes'
                return
            }

            if (minutes >= 1440 && minutes % 1440 === 0) {
                this.frequencyValue = minutes / 1440
                this.frequencyUnit = 'days'
            } else if (minutes >= 60 && minutes % 60 === 0) {
                this.frequencyValue = minutes / 60
                this.frequencyUnit = 'hours'
            } else {
                this.frequencyValue = minutes
                this.frequencyUnit = 'minutes'
            }
        },
        handleFrequencyValueChange() {
            this.calculateTotalMinutes()
        },
        handleFrequencyUnitChange() {
            const currentMinutes = this.calculateTotalMinutes()
            this.parseFrequencyFromMinutes(currentMinutes)
            this.calculateTotalMinutes()
        },
        showAddProjectDialog() {
            this.newProjectForm = {
                project_name: ''
            }
            this.addProjectDialogVisible = true
            this.$nextTick(() => {
                this.$refs.projectForm && this.$refs.projectForm.clearValidate()
            })
        },
        async addNewProject() {
            this.$refs.projectForm.validate(async valid => {
                if (valid) {
                    try {
                        this.addingProject = true
                        await this.$http.post('', {
                            api: '/api/projectTeam/add',
                            data: this.newProjectForm
                        })
                        this.$message.success('新增项目组成功')
                        this.addProjectDialogVisible = false
                        await this.fetchProjectTeams()
                        if (this.projectTeams.length > 0) {
                            const newProject = this.projectTeams.find(project => project.project_name === this.newProjectForm.project_name)
                            if (newProject) {
                                this.form.linked_project_team_name = newProject.project_name
                            }
                        }
                    } catch (error) {
                        this.$message.error('新增项目组失败')
                        console.error('Error adding new project:', error)
                    } finally {
                        this.addingProject = false
                    }
                }
            })
        },
        showAddTaskTypeDialog() {
            this.newTaskTypeForm = {
                task_type_name: '',
                task_type_description: '',
                recommended_effect_param_codes: '',
                effect_param_relationships_note: '',
                is_content_from_external: '1',
                is_bilateral_pref_consideration_needed: '0',
                linked_platform_ids: '',
                task_type_status: 1,
                task_type_owner: 2
            }
            this.selectedPlatformIds = []
            this.selectedEffectParams = []
            this.availableEffectParamsForTaskType = []
            this.addTaskTypeDialogVisible = true
            this.$nextTick(() => {
                this.$refs.taskTypeForm && this.$refs.taskTypeForm.clearValidate()
            })
        },
        async handlePlatformSelectChange(selectedIds) {
            this.newTaskTypeForm.linked_platform_ids = selectedIds.join(',')

            await this.fetchEffectParamsForTaskType(selectedIds)
        },
        async fetchEffectParamsForTaskType(platformIds) {
            if (platformIds.length === 0) {
                this.availableEffectParamsForTaskType = []
                this.selectedEffectParams = []
                this.newTaskTypeForm.recommended_effect_param_codes = ''
                return
            }

            try {
                const platformParamsArrays = []
                for (const platformId of platformIds) {
                    const params = await this.fetchPlatformEffectParams(platformId)
                    platformParamsArrays.push(params)
                }

                const allParams = []
                const seenCodes = new Set()

                platformParamsArrays.forEach(platformParams => {
                    platformParams.forEach(param => {
                        if (!seenCodes.has(param.effect_param_code)) {
                            seenCodes.add(param.effect_param_code)
                            allParams.push(param)
                        }
                    })
                })

                this.availableEffectParamsForTaskType = allParams

                this.selectedEffectParams = []
                this.newTaskTypeForm.recommended_effect_param_codes = ''

            } catch (error) {
                console.error('Error fetching effect params for task type:', error)
                this.$message.error('获取效果参数失败')
                this.availableEffectParamsForTaskType = []
            }
        },
        handleEffectParamsSelectChange(selectedCodes) {
            this.newTaskTypeForm.recommended_effect_param_codes = JSON.stringify(selectedCodes)
        },
        async addNewTaskType() {
            this.$refs.taskTypeForm.validate(async valid => {
                if (valid) {
                    try {
                        this.addingTaskType = true
                        await this.$http.post('', {
                            api: '/api/taskType/add',
                            data: this.newTaskTypeForm
                        })
                        this.$message.success('新增任务类型成功')
                        this.addTaskTypeDialogVisible = false
                        await this.fetchTaskTypes()
                    } catch (error) {
                        this.$message.error('新增任务类型失败')
                        console.error('Error adding new task type:', error)
                    } finally {
                        this.addingTaskType = false
                    }
                }
            })
        }
    },
    async created() {
        await this.fetchPlatforms({ page: 1, pageSize: 100 })
        await this.fetchMorphTypes()
        await this.fetchProjectTeams()
        await this.fetchTaskTypes()
        await this.fetchSceneTypes()
        await this.fetchSceneDetail()
        await this.fetchModalityOptions()
    }
}
</script>

<style scoped>
.edit-scene {
    padding: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.el-steps {
    margin-bottom: 30px;
}

.platform-configs {
    margin-top: 20px;
    margin-bottom: 20px;
}

.platform-configs h3 {
    margin-bottom: 16px;
    color: #303133;
    font-size: 16px;
}

.platform-card {
    margin-bottom: 16px;
}

.platform-card:last-child {
    margin-bottom: 0;
}

.data-options-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-item {
    margin-right: 0;
    margin-bottom: 0;
    white-space: nowrap;
}

.platform-selection-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.platform-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.platform-checkbox-item {
    margin-right: 0;
    margin-bottom: 0;
    white-space: nowrap;
}

.edit-breadcrumb {
    font-size: 14px;
    margin-bottom: 18px;
}

.edit-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 18px;
}

.navigation-buttons {
    margin-top: 30px;
}

.platform-selection-tip {
    margin-bottom: 16px;
}

.no-platforms-tip {
    margin-top: 16px;
}

.loading-tip {
    margin-top: 16px;
}

.effect-params-container {
    margin-top: 16px;
}

.effect-param-checkbox {
    margin-right: 16px;
    margin-bottom: 8px;
}

.effect-params-table {
    margin-top: 16px;
}

.effect-params-table h4 {
    margin-bottom: 12px;
    color: #303133;
    font-size: 14px;
}



.table-header-with-tooltip {
    cursor: help;
    display: flex;
    align-items: center;
    gap: 4px;
}

.table-header-with-tooltip .el-icon-question {
    color: #609399;
    font-size: 14px;
}
</style>
