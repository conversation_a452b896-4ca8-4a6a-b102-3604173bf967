/**
 * 型态相关的工具函数
 */

/**
 * 解析数组数据，支持多种格式
 * @param {*} data - 需要解析的数据
 * @returns {Array} 解析后的数组
 */
export function parseArrayData(data) {
    if (!data) {
        return []
    }
    
    if (Array.isArray(data)) {
        return data
    }
    
    if (typeof data === 'string') {
        // 处理 JSON 格式的字符串数组，如 "[1,2,3]"
        if (data.startsWith('[') && data.endsWith(']')) {
            try {
                const parsed = JSON.parse(data)
                return Array.isArray(parsed) ? parsed : []
            } catch (error) {
                console.error('解析 JSON 数组失败:', error)
                // 如果 JSON 解析失败，尝试手动解析
                return data.replace(/[\[\]]/g, '').split(',').map(item => {
                    const trimmed = item.trim()
                    const num = parseInt(trimmed)
                    return isNaN(num) ? trimmed : num
                }).filter(item => item !== '')
            }
        } else {
            // 处理逗号分隔的字符串，如 "1,2,3"
            return data.split(',').map(item => {
                const trimmed = item.trim()
                const num = parseInt(trimmed)
                return isNaN(num) ? trimmed : num
            }).filter(item => item !== '')
        }
    }
    
    return []
}

/**
 * 根据平台的 published_morph_ids 过滤型态选项
 * @param {Array} morphTypes - 所有型态选项数组
 * @param {Object} platform - 平台对象，包含 published_morph_ids 属性
 * @returns {Array} 过滤后的型态选项数组
 */
export function getFilteredMorphTypes(morphTypes, platform) {
    if (!morphTypes || !Array.isArray(morphTypes)) {
        return []
    }
    
    // 如果平台没有 published_morph_ids，返回所有型态
    if (!platform || !platform.published_morph_ids) {
        return morphTypes
    }
    
    // 使用 parseArrayData 解析 published_morph_ids
    const publishedIds = parseArrayData(platform.published_morph_ids).map(id => String(id))
    
    // 过滤型态选项，只返回 ID 在 publishedIds 中的项
    return morphTypes.filter(morphType => {
        return publishedIds.includes(String(morphType.ID))
    })
}
